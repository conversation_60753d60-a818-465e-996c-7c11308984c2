package com.fls.stock.service.woekteam.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.fls.common.core.exception.ServiceException;
import com.fls.common.mybatis.core.page.PageResult;
import com.fls.master.api.RemoteResourceService;
import com.fls.stock.annotation.AuthFilter;
import com.fls.stock.convert.WorkTeamStockConvert;
import com.fls.stock.entity.WorkteamStock;
import com.fls.stock.entity.WorkteamStockLockRecord;
import com.fls.stock.enums.LockTypeEnum;
import com.fls.stock.mapper.WorkteamStockLockRecordMapper;
import com.fls.stock.pojo.model.BaseSourceBill;
import com.fls.stock.pojo.query.TeamStockLockQuery;
import com.fls.stock.pojo.vo.TeamStockLockRecordVo;
import com.fls.stock.service.woekteam.IWorkteamStockLockRecordService;
import com.fls.upms.api.RemoteUserService;
import com.github.yulichang.base.MPJBaseServiceImpl;
import com.github.yulichang.wrapper.MPJLambdaWrapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

/**
 * 班组库存锁定记录表(WorkteamStockLockRecord)表服务实现类
 *
 * <AUTHOR>
 * @since 2024-12-11 09:08:53
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class WorkteamStockLockRecordServiceImpl extends MPJBaseServiceImpl<WorkteamStockLockRecordMapper, WorkteamStockLockRecord> implements IWorkteamStockLockRecordService {

    private final WorkTeamStockConvert workTeamStockConvert;

    @DubboReference
    private RemoteUserService remoteUserService;

    @DubboReference
    private RemoteResourceService remoteResourceService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void createWorkteamStockLockRecord(BaseSourceBill sourceBill, List<WorkteamStock> stocks, boolean isLock) {
        List<WorkteamStockLockRecord> lockRecords = processLockRecords(sourceBill, stocks, isLock);
        // 如果是释放库存操作，需要更新已存在的锁定记录
        if (!isLock) {
            List<WorkteamStockLockRecord> updateRecords = new ArrayList<>();
            for (WorkteamStock stock : stocks) {
                // 查找已存在的锁定记录
                List<WorkteamStockLockRecord> existingLockRecords = lambdaQuery()
                    .eq(WorkteamStockLockRecord::getIdStock, stock.getIdStock())
                    .eq(WorkteamStockLockRecord::getSourceBillCode, sourceBill.getSourceBillCode())
                    .eq(WorkteamStockLockRecord::getOperateType, LockTypeEnum.LOCK.getType())
                    .list();
                if (ObjectUtil.isNotEmpty(existingLockRecords)) {
                    BigDecimal releaseQuantity = stock.getChanges();
                    BigDecimal totalLockedQuantity = existingLockRecords.stream()
                        .map(WorkteamStockLockRecord::getLockNum)
                        .reduce(BigDecimal.ZERO, BigDecimal::add);
                    // 校验释放数量不能大于锁定数量
                    if (releaseQuantity.compareTo(totalLockedQuantity) > 0) {
                        throw new ServiceException("释放数量不能大于锁定数量");
                    }
                    // 更新锁定记录的数量
                    for (WorkteamStockLockRecord existingRecord : existingLockRecords) {
                        if (releaseQuantity.compareTo(BigDecimal.ZERO) > 0) {
                            BigDecimal currentLocked = existingRecord.getLockNum();
                            if (releaseQuantity.compareTo(currentLocked) >= 0) {
                                existingRecord.setLockNum(BigDecimal.ZERO);
                                releaseQuantity = releaseQuantity.subtract(currentLocked);
                            } else {
                                existingRecord.setLockNum(currentLocked.subtract(releaseQuantity));
                                releaseQuantity = BigDecimal.ZERO;
                            }
                            updateRecords.add(existingRecord);
                        }
                    }
                }
            }
            if (ObjectUtil.isNotEmpty(updateRecords)) {
                lockRecords.addAll(updateRecords);
            }
        }
        saveOrUpdateBatch(lockRecords);
    }

    private List<WorkteamStockLockRecord> processLockRecords(BaseSourceBill sourceBill, List<WorkteamStock> stocks, boolean isLock) {
        List<WorkteamStockLockRecord> lockRecords = new ArrayList<>();
        String operateType = isLock ? LockTypeEnum.LOCK.getType() : LockTypeEnum.UNLOCK.getType();

        for (WorkteamStock stock : stocks) {
            // 查询现有记录，使用list兼容历史脏数据
            List<WorkteamStockLockRecord> existingRecords = lambdaQuery()
                .eq(WorkteamStockLockRecord::getSourceBillCode, sourceBill.getSourceBillCode())
                .eq(WorkteamStockLockRecord::getIdStock, stock.getIdStock())
                .eq(WorkteamStockLockRecord::getOperateType, operateType)
                .list();

            if (ObjectUtil.isNotEmpty(existingRecords)) {
                // 如果有多条记录，取第一条进行更新，其余记录可考虑删除或合并
                WorkteamStockLockRecord existingRecord = existingRecords.get(0);
                // 在原有记录基础上增加数量
                existingRecord.setLockNum(existingRecord.getLockNum().add(stock.getChanges()));
                existingRecord.setUpdater(sourceBill.getOperator());
                existingRecord.setUpdateName(remoteUserService.getUserNameByUserId(sourceBill.getOperator()));
                lockRecords.add(existingRecord);

                // 如果有多条重复记录，记录日志
                if (existingRecords.size() > 1) {
                    log.warn("发现重复的库存锁定记录，单号：{}，库存ID：{}，操作类型：{}，记录数：{}",
                        sourceBill.getSourceBillCode(), stock.getIdStock(), operateType, existingRecords.size());
                }
            } else {
                // 生成新的库存锁定/释放记录
                String userName = remoteUserService.getUserNameByUserId(sourceBill.getOperator());
                WorkteamStockLockRecord lockRecord = workTeamStockConvert.stockToLock(stock);
                // 单据属性填充
                lockRecord.setSourceBillCode(sourceBill.getSourceBillCode());
                lockRecord.setIdSourceBill(sourceBill.getIdSourceBill());
                lockRecord.setIdSourceRes(sourceBill.getIdSourceRes());
                lockRecord.setSourceResName(sourceBill.getSourceResName());
                lockRecord.setFirstSourceBillCode(sourceBill.getFirstSourceCode());
                lockRecord.setIdFirstSourceBill(sourceBill.getIdFirstSourceBill());
                lockRecord.setIdFirstRes(sourceBill.getIdFirstRes());
                lockRecord.setFirstResName(sourceBill.getFirstResName());
                lockRecord.setCreator(sourceBill.getOperator());
                lockRecord.setCreateName(userName);
                lockRecord.setUpdater(sourceBill.getOperator());
                lockRecord.setUpdateName(userName);
                lockRecord.setOperateType(operateType);
                lockRecords.add(lockRecord);
            }
        }

        return lockRecords;
    }

    @Override
    @AuthFilter
    public PageResult<TeamStockLockRecordVo> getLockRecordPage(TeamStockLockQuery lockQuery) {
        MPJLambdaWrapper<WorkteamStockLockRecord> transLogDetailWrapper = new MPJLambdaWrapper<>();
        transLogDetailWrapper.selectAll(WorkteamStockLockRecord.class)
            .like(StrUtil.isNotBlank(lockQuery.getMaterialName()), WorkteamStockLockRecord::getMaterialName, lockQuery.getMaterialName())
            .ge(StrUtil.isNotBlank(lockQuery.getBeginDate()), WorkteamStockLockRecord::getCreateTime, lockQuery.getBeginDate())
            .le(StrUtil.isNotBlank(lockQuery.getEndDate()), WorkteamStockLockRecord::getCreateTime, lockQuery.getEndDate())
            .eq(StrUtil.isNotBlank(lockQuery.getIdBizunit()), WorkteamStockLockRecord::getIdBizunit, lockQuery.getIdBizunit())
            .gt(WorkteamStockLockRecord::getLockNum, BigDecimal.ZERO)
            .eq(StrUtil.isNotBlank(lockQuery.getLockType()), WorkteamStockLockRecord::getOperateType, lockQuery.getLockType())
            .eq(StrUtil.isNotBlank(lockQuery.getBillCode()), WorkteamStockLockRecord::getSourceBillCode, lockQuery.getBillCode())
            .eq(StrUtil.isNotBlank(lockQuery.getIdMaterial()), WorkteamStockLockRecord::getIdMaterial, lockQuery.getIdMaterial())
            .in(CollUtil.isNotEmpty(lockQuery.getAuthBizunitIds()), WorkteamStockLockRecord::getIdBizunit, lockQuery.getAuthBizunitIds())
            .orderByDesc(WorkteamStockLockRecord::getOperateTime);
        Page<TeamStockLockRecordVo> page = new Page<>(lockQuery.getPageNo(), lockQuery.getPageSize());
        Page<TeamStockLockRecordVo> result = selectJoinListPage(page, TeamStockLockRecordVo.class, transLogDetailWrapper);
        return new PageResult<>(page, result.getRecords());
    }
}

