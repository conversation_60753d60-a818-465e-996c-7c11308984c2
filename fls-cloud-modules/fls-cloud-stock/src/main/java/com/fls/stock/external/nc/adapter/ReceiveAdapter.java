package com.fls.stock.external.nc.adapter;

import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.fls.stock.config.NcParamConfig;
import com.fls.stock.enums.BillTypeEnum;
import com.fls.stock.external.nc.handle.order.NcOrderFactory;
import com.fls.stock.external.nc.handle.order.NcOrderHandle;
import com.fls.stock.external.nc.pojo.dto.PrepareMaterialResultDTO;
import com.fls.stock.pojo.dto.OtherInDTO;
import com.fls.stock.pojo.dto.PrepareMaterialContextDTO;
import com.fls.stock.pojo.vo.ReceiveVO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Component;

/**
 * @Description:
 * @Author: caibenwei
 * @DATE: 2024/12/20 11:46
 */
@Component
@RequiredArgsConstructor
@Slf4j
public class ReceiveAdapter {

    private final NcOrderFactory ncOrderFactory;

    private final NcParamConfig ncParamConfig;

    public ReceiveVO onReceive(Boolean hasSameOrg, PrepareMaterialContextDTO ctx) {
        BillTypeEnum orderType;
        JSONObject param = JSONUtil.createObj();
        param.set("vdef12",ctx.getBillCode());
        param.set("vnotebody",ctx.getAssetCode());
        if (hasSameOrg) {
            orderType = BillTypeEnum.TRANSFER;
            param.set("vdef15", "2");
            param.set("vtrantypecode",ncParamConfig.getReceiveTransferVtrantypecode()); //转库单交易类型编码:维修转库（实发）
            ctx.setOrderParamMaps(param);
        } else {
            orderType = BillTypeEnum.ALLOCATE;
            param.set("ctaxcodeid", ncParamConfig.getReceiveALlocateCtaxcodeid()); //税码
            param.set("ctrantypeid", ncParamConfig.getReceiveALlocateCtrantypeid()); // 交易类型：调拨订单（自动出入库），根据5X-Cxx-011查询
            ctx.setOrderParamMaps(param);
        }
        NcOrderHandle ncOrderHandle = ncOrderFactory.getHandle(orderType.name());
        PrepareMaterialResultDTO operate = ncOrderHandle.createOperate(ctx);
        OtherInDTO otherInDTO = ncOrderHandle.supplementOtherInInfo(operate.getPkNcBill());
        ReceiveVO vo = new ReceiveVO();
        BeanUtils.copyProperties(operate, vo);
        BeanUtils.copyProperties(otherInDTO, vo);
        return vo;
    }
}
