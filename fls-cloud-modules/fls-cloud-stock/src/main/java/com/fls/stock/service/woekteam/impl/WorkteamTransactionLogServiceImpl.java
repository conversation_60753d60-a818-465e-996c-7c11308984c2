package com.fls.stock.service.woekteam.impl;

import com.fls.stock.convert.WorkTeamStockConvert;
import com.fls.stock.entity.WorkteamStock;
import com.fls.stock.entity.WorkteamTransactionDetail;
import com.fls.stock.entity.WorkteamTransactionLog;
import com.fls.stock.enums.BoundTypeEnum;
import com.fls.stock.enums.TransLogTypeEnum;
import com.fls.stock.mapper.WorkteamTransactionLogMapper;
import com.fls.stock.pojo.model.BaseSourceBill;
import com.fls.stock.service.woekteam.IWorkteamTransactionDetailService;
import com.fls.stock.service.woekteam.IWorkteamTransactionLogService;
import com.fls.upms.api.RemoteUserService;
import com.github.yulichang.base.MPJBaseServiceImpl;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Service;

import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 班组库存流水表(WorkteamTransactionLog)表服务实现类
 *
 * <AUTHOR>
 * @since 2024-12-11 09:13:44
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class WorkteamTransactionLogServiceImpl extends MPJBaseServiceImpl<WorkteamTransactionLogMapper, WorkteamTransactionLog> implements IWorkteamTransactionLogService {

    private final IWorkteamTransactionDetailService transactionDetailService;

    private final WorkTeamStockConvert workTeamStockConvert;

    @DubboReference
    private RemoteUserService remoteUserService;

    private static final SimpleDateFormat FORMATTER = new SimpleDateFormat("yyyyMMddHHmmssSSS");

    @Override
    public void generateTransactionLog(BaseSourceBill bill, List<WorkteamStock> records, TransLogTypeEnum logType) {
        WorkteamTransactionLog transactionLog = workTeamStockConvert.billToTransLog(bill);
        String userName = remoteUserService.getUserNameByUserId(bill.getOperator());
        transactionLog.setCreateName(userName);
        transactionLog.setTransactionCode(FORMATTER.format(new Date()));
        transactionLog.setLogType(logType.getType());
        transactionLog.setStockTime(LocalDateTime.now());
        transactionLog.setSourceDesc(BoundTypeEnum.getEnumDesc(bill.getSourceBillType()));
        boolean saved = save(transactionLog);
        if (!saved) {
            log.error("save transaction log failed, sourceBill: {}", bill);
        }
        //库存流水明细更新，进到方法内的肯定都是合并之后的库存流水
        List<WorkteamTransactionDetail> transactionDetails = records.stream().map(stock -> {
            WorkteamTransactionDetail detail = workTeamStockConvert.stockToTransDetail(stock);
            transactionDetailService.fillExProperties(detail);
            detail.setIdTransactionLog(transactionLog.getIdTransactionLog());
            return detail;
        }).collect(Collectors.toList());
        transactionDetailService.saveBatch(transactionDetails);
        log.debug("generate transaction log success, sourceBill: {},detail counts : {}", bill, transactionDetails.size());
    }
}

