package com.fls.stock.service.woekteam.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.fls.common.core.excel.utils.ExcelUtil;
import com.fls.common.mybatis.core.page.PageResult;
import com.fls.master.api.RemoteResourceService;
import com.fls.master.api.model.ResourceInfo;
import com.fls.stock.annotation.AuthFilter;
import com.fls.stock.entity.WorkteamTransactionDetail;
import com.fls.stock.entity.WorkteamTransactionLog;
import com.fls.stock.enums.TransLogTypeEnum;
import com.fls.stock.mapper.WorkteamTransactionDetailMapper;
import com.fls.stock.pojo.query.BaseAuthQuery;
import com.fls.stock.pojo.query.TransLogDetailQuery;
import com.fls.stock.pojo.vo.TransactionLodDetailVO;
import com.fls.stock.service.woekteam.IWorkteamTransactionDetailService;
import com.github.yulichang.base.MPJBaseServiceImpl;
import com.github.yulichang.wrapper.MPJLambdaWrapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 班组库存流水明细表(WorkteamTransactionDetail)表服务实现类
 *
 * <AUTHOR>
 * @since 2024-12-11 09:08:06
 */
@Service
@Slf4j
public class WorkteamTransactionDetailServiceImpl extends MPJBaseServiceImpl<WorkteamTransactionDetailMapper, WorkteamTransactionDetail>
    implements IWorkteamTransactionDetailService {

    @DubboReference
    private RemoteResourceService remoteResourceService;

    @Override
    public void fillExProperties(WorkteamTransactionDetail detail) {

    }

    @Override
    @AuthFilter
    public PageResult<TransactionLodDetailVO> getTransLogPage(TransLogDetailQuery transLogDetailQuery) {
        MPJLambdaWrapper<WorkteamTransactionDetail> transLogDetailWrapper = new MPJLambdaWrapper<>();
        transLogDetailWrapper.selectAll(WorkteamTransactionDetail.class)
            .selectAll(WorkteamTransactionLog.class)
            .leftJoin(WorkteamTransactionLog.class, WorkteamTransactionLog::getIdTransactionLog, WorkteamTransactionDetail::getIdTransactionLog)
            .eq(ObjectUtil.isNotNull(transLogDetailQuery.getType()), WorkteamTransactionLog::getLogType, transLogDetailQuery.getType())
            .eq(StrUtil.isNotBlank(transLogDetailQuery.getIdMaterial()), WorkteamTransactionDetail::getIdMaterial, transLogDetailQuery.getIdMaterial())
            .ge(StrUtil.isNotBlank(transLogDetailQuery.getBeginDate()), WorkteamTransactionDetail::getCreateTime, transLogDetailQuery.getBeginDate())
            .le(StrUtil.isNotBlank(transLogDetailQuery.getEndDate()), WorkteamTransactionDetail::getCreateTime, transLogDetailQuery.getEndDate())
            .in(CollUtil.isNotEmpty(transLogDetailQuery.getAuthBizunitIds()), WorkteamTransactionDetail::getIdBizunit, transLogDetailQuery.getAuthBizunitIds())
            .orderByDesc(WorkteamTransactionDetail::getCreateTime);

        boolean hasName = StrUtil.isNotBlank(transLogDetailQuery.getName());
        boolean hasCode = StrUtil.isNotBlank(transLogDetailQuery.getCode());
        boolean hasKeyword = StrUtil.isNotBlank(transLogDetailQuery.getMaterialName());
        if (hasKeyword) {
            transLogDetailWrapper.and(w -> w
                .like(WorkteamTransactionDetail::getMaterialName, transLogDetailQuery.getMaterialName())
                .or()
                .like(WorkteamTransactionDetail::getMaterialCode, transLogDetailQuery.getMaterialName())
                .or()
                .like(WorkteamTransactionDetail::getMaterialParam, transLogDetailQuery.getMaterialName())
            );
        }
        if (hasName) {
            transLogDetailWrapper.and(w -> w
                .like(StrUtil.isNotBlank(transLogDetailQuery.getName()), WorkteamTransactionDetail::getWorkteamName, transLogDetailQuery.getName())
                .or()
                .like(StrUtil.isNotBlank(transLogDetailQuery.getName()), WorkteamTransactionDetail::getOrgName, transLogDetailQuery.getName())
                .or()
                .like(StrUtil.isNotBlank(transLogDetailQuery.getName()), WorkteamTransactionDetail::getBizunitName, transLogDetailQuery.getName())
            );
        }
        if (hasCode) {
            transLogDetailWrapper.and(w -> w
                .eq(StrUtil.isNotBlank(transLogDetailQuery.getCode()), WorkteamTransactionLog::getTransactionCode, transLogDetailQuery.getCode())
                .or()
                .like(StrUtil.isNotBlank(transLogDetailQuery.getCode()), WorkteamTransactionLog::getSourceBillCode, transLogDetailQuery.getCode())
            );
        }
        Page<TransactionLodDetailVO> page = new Page<>(transLogDetailQuery.getPageNo(), transLogDetailQuery.getPageSize());
        Page<TransactionLodDetailVO> result = selectJoinListPage(page, TransactionLodDetailVO.class, transLogDetailWrapper);
        if (ObjectUtil.isNotEmpty(result)) {
            List<String> idsResources = result.getRecords().stream().map(TransactionLodDetailVO::getIdSourceRes).distinct().collect(Collectors.toList());
            List<ResourceInfo> resourceInfos = remoteResourceService.getResourcesByIds(idsResources);
            Map<String, ResourceInfo> workTeamInfoMap =
                resourceInfos.stream().collect(Collectors.toMap(ResourceInfo::getIdResource, resourceInfo -> resourceInfo));

            result.getRecords().forEach(record -> {
                ResourceInfo resourceInfo = workTeamInfoMap.get(record.getIdSourceRes());
                if (ObjectUtil.isNotNull(resourceInfo)) {
                    record.setVisitPath(resourceInfo.getIncalLink());
                    record.setAppletVisitPath(resourceInfo.getAppLink());
                }
            });
        }
        result.getRecords().forEach(record -> {
            record.setLogTypeDesc(TransLogTypeEnum.getDescByType(record.getLogType()));
        });
        return new PageResult<>(page, result.getRecords());
    }

    @Override
    @AuthFilter
    public void exportTransLog(BaseAuthQuery authQuery, HttpServletResponse response) {
        MPJLambdaWrapper<WorkteamTransactionDetail> transLogDetailWrapper = new MPJLambdaWrapper<>();
        transLogDetailWrapper.selectAll(WorkteamTransactionDetail.class)
            .selectAll(WorkteamTransactionLog.class)
            .leftJoin(WorkteamTransactionLog.class, WorkteamTransactionLog::getIdTransactionLog, WorkteamTransactionDetail::getIdTransactionLog)
            .in(CollUtil.isNotEmpty(authQuery.getAuthBizunitIds()), WorkteamTransactionDetail::getIdBizunit, authQuery.getAuthBizunitIds())
            .orderByDesc(WorkteamTransactionDetail::getCreateTime);
        List<TransactionLodDetailVO> result = selectJoinList(TransactionLodDetailVO.class, transLogDetailWrapper);
        ExcelUtil.exportExcel(result, "库存流水记录", TransactionLodDetailVO.class, response);
    }
}

