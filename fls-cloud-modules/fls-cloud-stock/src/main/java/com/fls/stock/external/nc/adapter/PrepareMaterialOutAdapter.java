package com.fls.stock.external.nc.adapter;

import com.fls.stock.entity.BaseWarehouse;
import com.fls.stock.enums.BillTypeEnum;
import com.fls.stock.external.nc.handle.order.NcOrderFactory;
import com.fls.stock.external.nc.handle.order.NcOrderHandle;
import com.fls.stock.external.nc.pojo.dto.MaterialOutDTO;
import com.fls.stock.pojo.dto.PrepareMaterialOutDTO;
import com.fls.stock.service.invoke.BaseUserService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * @Description:
 * @Author: caibenwei
 * @DATE: 2024/12/25 9:46
 */
@Component
@RequiredArgsConstructor
@Slf4j
public class PrepareMaterialOutAdapter {


    private final NcOrderFactory ncOrderFactory;

    private final BaseUserService userService;

    public void materialOut(PrepareMaterialOutDTO dto, BaseWarehouse warehouse) {
        //获取用户nc主键
        String userNcPk = userService.getUserNcPk(dto.getUserCode());
        NcOrderHandle handle = ncOrderFactory.getHandle(dto.getBillType().toUpperCase());
        MaterialOutDTO.MaterialOutDTOBuilder builder = MaterialOutDTO.builder()
            .approver(userNcPk);
        if (BillTypeEnum.TRANSFER.name().equalsIgnoreCase(dto.getBillType())) {
            builder.cgeneralhid(dto.getPkIcOut())
                .cwarehouseid(warehouse.getCode());
        } else if (BillTypeEnum.ALLOCATE.name().equalsIgnoreCase(dto.getBillType())) {
            builder.cgeneralhid(dto.getPkNcBill())
                .cwarehouseid(warehouse.getCode());
        }
        handle.outOperate(builder.build());
    }
}
