package com.fls.stock.external.nc.adapter;

import cn.hutool.core.lang.Assert;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.fls.common.core.constant.CommonConstants;
import com.fls.common.core.exception.ServiceException;
import com.fls.stock.config.NcParamConfig;
import com.fls.stock.entity.IcGeneraloutB;
import com.fls.stock.enums.BillTypeEnum;
import com.fls.stock.external.nc.handle.order.NcOrderFactory;
import com.fls.stock.external.nc.handle.order.NcOrderHandle;
import com.fls.stock.external.nc.pojo.dto.MaterialOutDTO;
import com.fls.stock.external.nc.pojo.dto.PrepareMaterialResultDTO;
import com.fls.stock.mapper.IcGeneraloutBMapper;
import com.fls.stock.pojo.dto.PrepareMaterialContextDTO;
import com.fls.stock.service.invoke.BaseUserService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * @Description:
 * @Author: caibenwei
 * @DATE: 2024/12/20 11:48
 */
@Component
@RequiredArgsConstructor
@Slf4j
public class MaterialReturnAdapter {

    private final NcOrderFactory ncOrderFactory;

    private final IcGeneraloutBMapper icGeneraloutBMapper;

    private final NcParamConfig ncParamConfig;

    private final BaseUserService userService;

    public PrepareMaterialResultDTO onMaterialReturn(boolean hasSameOrg, PrepareMaterialContextDTO ctx) {
        BillTypeEnum orderType;
        JSONObject param = JSONUtil.createObj();
        if (hasSameOrg) {
            orderType = BillTypeEnum.TRANSFER;
            param.set("vdef15", "1");
            param.set("vtrantypecode", ncParamConfig.getLockTransferVtrantypecode()); // 转库单交易类型编码:维修转库（实发）
            ctx.setOrderParamMaps(param);
        } else {
            orderType = BillTypeEnum.ALLOCATE;
            param.set("cbiztypeid", ncParamConfig.getReturnALlocateCbiztypeid()); //业务类型:调拨订单-总部
            param.set("ctaxcodeid", ncParamConfig.getReturnALlocateCtaxcodeid()); //税码
            param.set("ctrantypeid", ncParamConfig.getReturnALlocateCtrantypeid()); // 交易类型：调拨订单（基地维修），根据5X-Cxx-010查询
            ctx.setOrderParamMaps(param);
        }
        NcOrderHandle ncOrderHandle = ncOrderFactory.getHandle(orderType.name());
        PrepareMaterialResultDTO resultDTO = ncOrderHandle.createOperate(ctx);
        return resultDTO;

    }

    public Object onMaterialReturnOut(PrepareMaterialContextDTO ctx, PrepareMaterialResultDTO resultDTO) {
        String userNcPk = userService.getUserNcPk(ctx.getUserCode());
        MaterialOutDTO.MaterialOutDTOBuilder builder = MaterialOutDTO.builder()
            .approver(userNcPk);
        if (BillTypeEnum.TRANSFER.name().equalsIgnoreCase(resultDTO.getBillType())) {
            // 根据转库单查询出库单pk，作为入参
            IcGeneraloutB icGeneraloutB = icGeneraloutBMapper.selectOne(new LambdaQueryWrapper<IcGeneraloutB>()
                .eq(IcGeneraloutB::getDr, CommonConstants.DELETE_FLAG_NOT_DELETED)
                .eq(IcGeneraloutB::getCsourcebillhid, resultDTO.getPkNcBill())
                .last("AND ROWNUM = 1"));

            Assert.notNull(icGeneraloutB, () -> new ServiceException("nc普通出库单不存在！"));
            builder.cgeneralhid(icGeneraloutB.getCgeneralhid());
        } else if (BillTypeEnum.ALLOCATE.name().equalsIgnoreCase(resultDTO.getBillType())) {
            builder.cwarehouseid(ctx.getWarehouseOut().getCode());
            builder.cgeneralhid(resultDTO.getPkNcBill());
        }
        NcOrderHandle handle = ncOrderFactory.getHandle(resultDTO.getBillType());
        return handle.outOperate(builder.build());
    }

}
