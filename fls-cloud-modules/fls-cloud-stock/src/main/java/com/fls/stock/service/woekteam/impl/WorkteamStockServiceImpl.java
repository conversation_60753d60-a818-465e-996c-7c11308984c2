package com.fls.stock.service.woekteam.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.lang.Dict;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fls.common.core.exception.ServiceException;
import com.fls.common.mybatis.core.page.PageResult;
import com.fls.master.api.RemoteMasterDataService;
import com.fls.master.api.RemoteResourceService;
import com.fls.master.api.RemoteWorkTeamService;
import com.fls.master.api.model.ResourceInfo;
import com.fls.master.api.model.WorkTeamBaseInfo;
import com.fls.stock.annotation.AuthFilter;
import com.fls.stock.constant.StockConst;
import com.fls.stock.convert.WorkTeamStockConvert;
import com.fls.stock.entity.WorkteamInboundBill;
import com.fls.stock.entity.WorkteamOutboundBill;
import com.fls.stock.entity.WorkteamStock;
import com.fls.stock.enums.BoundTypeEnum;
import com.fls.stock.enums.TransLogTypeEnum;
import com.fls.stock.mapper.WorkteamStockMapper;
import com.fls.stock.pojo.dto.GroupTransferDTO;
import com.fls.stock.pojo.dto.StockExchangeDTO;
import com.fls.stock.pojo.dto.StockInboundDTO;
import com.fls.stock.pojo.dto.StockLockReleaseDTO;
import com.fls.stock.pojo.dto.StockOutboundDTO;
import com.fls.stock.pojo.model.BaseSourceBill;
import com.fls.stock.pojo.model.MaterialOperationRecord;
import com.fls.stock.pojo.model.StockOperationRecord;
import com.fls.stock.pojo.query.TeamStockDetailQuery;
import com.fls.stock.pojo.vo.TeamStockDetailVO;
import com.fls.stock.service.woekteam.ITWorkteamInboundBillService;
import com.fls.stock.service.woekteam.ITWorkteamOutboundBillService;
import com.fls.stock.service.woekteam.IWorkteamStockLockRecordService;
import com.fls.stock.service.woekteam.IWorkteamStockService;
import com.fls.stock.service.woekteam.IWorkteamTransactionLogService;
import com.fls.stock.utils.BillGenerator;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.function.Consumer;
import java.util.stream.Collectors;

/**
 * 工作班组库存表(WorkteamStock)表服务实现类
 *
 * <AUTHOR>
 * @since 2024-12-11 10:36:04
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class WorkteamStockServiceImpl extends ServiceImpl<WorkteamStockMapper, WorkteamStock> implements IWorkteamStockService {

    @DubboReference
    private final RemoteWorkTeamService remoteWorkTeamService;

    private final IWorkteamTransactionLogService transactionLogService;

    private final WorkTeamStockConvert workTeamStockConvert;

    private final ITWorkteamInboundBillService workteamInboundBillService;

    private final ITWorkteamOutboundBillService workteamOutboundBillService;

    private final IWorkteamStockLockRecordService workteamStockLockRecordService;

    @DubboReference
    private RemoteResourceService remoteResourceService;

    @DubboReference
    private RemoteMasterDataService masterDataService;

    @Override
    @AuthFilter
    public PageResult<TeamStockDetailVO> getWorkteamStockPage(TeamStockDetailQuery teamStockDetailQuery) {
        Page<WorkteamStock> page = new Page<>(teamStockDetailQuery.getPageNo(), teamStockDetailQuery.getPageSize());
        //用户id和班组id
        String userId = teamStockDetailQuery.getIdUser();
        List<String> idsWorkteam = teamStockDetailQuery.getIdsWorkteam();
        if (StrUtil.isNotBlank(userId)) {
            List<String> idsWorkteamByUser = remoteWorkTeamService.getWorkTeamsByUserId(userId).stream().map(WorkTeamBaseInfo::getIdWorkteam).collect(Collectors.toList());
            if (CollectionUtil.isNotEmpty(idsWorkteamByUser) && CollectionUtil.isNotEmpty(idsWorkteam)) {
                idsWorkteamByUser.retainAll(idsWorkteam);
                if (CollectionUtil.isEmpty(idsWorkteamByUser)) {
                    throw new ServiceException("指定用户班组信息不存在");
                }
            }
            idsWorkteam = idsWorkteamByUser;
        }
        //班组id集合查询库存明细
        LambdaQueryWrapper<WorkteamStock> wrapper = new LambdaQueryWrapper<WorkteamStock>().in(ObjectUtil.isNotEmpty(idsWorkteam), WorkteamStock::getIdWorkteam, idsWorkteam)
            .eq(StrUtil.isNotBlank(teamStockDetailQuery.getIdBizunit()), WorkteamStock::getIdBizunit, teamStockDetailQuery.getIdBizunit())
            .in(CollUtil.isNotEmpty(teamStockDetailQuery.getAuthBizunitIds()), WorkteamStock::getIdBizunit, teamStockDetailQuery.getAuthBizunitIds())
            .orderByDesc(WorkteamStock::getCreateTime);
        if (teamStockDetailQuery.isAvailable()) {
            wrapper.gt(WorkteamStock::getAvailableNum, BigDecimal.ZERO);
        }
        if (StrUtil.isNotBlank(teamStockDetailQuery.getKeyword())) {
            wrapper.and(w -> {
                w.like(StrUtil.isNotBlank(teamStockDetailQuery.getKeyword()), WorkteamStock::getMaterialName, teamStockDetailQuery.getKeyword()).or()
                    .like(StrUtil.isNotBlank(teamStockDetailQuery.getKeyword()), WorkteamStock::getMaterialCode, teamStockDetailQuery.getKeyword()).or()
                    .like(StrUtil.isNotBlank(teamStockDetailQuery.getKeyword()), WorkteamStock::getMaterialParam, teamStockDetailQuery.getKeyword()).or()
                    .like(StrUtil.isNotBlank(teamStockDetailQuery.getKeyword()), WorkteamStock::getBatchCode, teamStockDetailQuery.getKeyword());
            });
        }
        Page<WorkteamStock> pageResult = baseMapper.selectPage(page, wrapper);

        //page vo转换
        List<TeamStockDetailVO> details = workTeamStockConvert.toVoList(pageResult.getRecords());
        if (ObjectUtil.isNotEmpty(details)) {
            idsWorkteam = details.stream().map(TeamStockDetailVO::getIdWorkteam).distinct().collect(Collectors.toList());
            List<WorkTeamBaseInfo> workTeamBaseInfos = remoteWorkTeamService.getWorkTeamsByIds(idsWorkteam);
            Map<String, WorkTeamBaseInfo> workTeamInfoMap =
                workTeamBaseInfos.stream().collect(Collectors.toMap(WorkTeamBaseInfo::getIdWorkteam, workTeamBaseInfo -> workTeamBaseInfo));
            details.forEach(detail -> {
                WorkTeamBaseInfo workTeamInfo = workTeamInfoMap.get(detail.getIdWorkteam());
                if (ObjectUtil.isNotNull(workTeamInfo)) {
                    detail.setWorkteamName(workTeamInfo.getName());
                    detail.setWarehouseName(workTeamInfo.getWarehouseName());
                    detail.setWhposName(workTeamInfo.getWhposName());
                    detail.setBizName(workTeamInfo.getBizunitName());
                    detail.setHeadName(workTeamInfo.getHeadmanName());
                    detail.setIdHeadman(workTeamInfo.getIdHeadman());
                }
            });
        }
        return new PageResult<>(page, details);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void groupTransfer(GroupTransferDTO groupTransfer) {
        // 处理库存出库
        StockOutboundDTO stockOutboundDTO = workTeamStockConvert.transferToOutboundDTO(groupTransfer);
        stockOutboundDTO.setSourceBillType(BoundTypeEnum.TRANSFER.getType());
        stockOutbound(stockOutboundDTO);
        // 处理库存入库
        StockInboundDTO stockInboundDTO = handleStockInbound(groupTransfer);
        stockInbound(stockInboundDTO);
    }

    private StockInboundDTO handleStockInbound(GroupTransferDTO groupTransfer) {
        StockInboundDTO stockInboundDTO = workTeamStockConvert.transferToInboundDTO(groupTransfer);

        // 生成记录映射
        Map<String, MaterialOperationRecord> recordMap = createRecordMap(stockInboundDTO);

        // 查询现有库存
        List<WorkteamStock> stocks = queryExistingStocks(recordMap);

        // 合并记录
        List<MaterialOperationRecord> materialOperationRecords = mergeStockRecords(stocks, recordMap);

        // 设置入库记录
        stockInboundDTO.setRecords(materialOperationRecords);
        stockInboundDTO.setSourceBillType(BoundTypeEnum.TRANSFER.getType());
        return stockInboundDTO;
    }

    private Map<String, MaterialOperationRecord> createRecordMap(StockInboundDTO stockInboundDTO) {
        return stockInboundDTO.getRecords().stream().collect(Collectors.toMap(MaterialOperationRecord::getIdStock, record -> record, (existing, newRecord) -> {
            existing.setQuantity(existing.getQuantity().add(newRecord.getQuantity()));
            return existing;
        }));
    }

    private List<WorkteamStock> queryExistingStocks(Map<String, MaterialOperationRecord> recordMap) {
        return lambdaQuery().in(CollectionUtil.isNotEmpty(recordMap.keySet()), WorkteamStock::getIdStock, recordMap.keySet()).list();
    }

    private List<MaterialOperationRecord> mergeStockRecords(List<WorkteamStock> stocks, Map<String, MaterialOperationRecord> recordMap) {
        return stocks.stream().map(stock -> {
            MaterialOperationRecord materialOperationRecord = workTeamStockConvert.stockToMaterial(stock);
            MaterialOperationRecord originRecord = recordMap.get(materialOperationRecord.getIdStock());
            if (ObjectUtil.isNotNull(originRecord)) {
                materialOperationRecord.setQuantity(originRecord.getQuantity());
            }
            materialOperationRecord.setIdStock(null);
            return materialOperationRecord;
        }).collect(Collectors.toList());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String stockOutbound(StockOutboundDTO outbound) {
        //先进行库存释放操作
        stockRelease(outbound);
        //校验并填充资源名称
        checkAndFillResNam(outbound);
        // 验证班组信息是否存在
        WorkTeamBaseInfo workTeamInfo = validateWorkTeam(outbound.getIdWorkteam());
        // 合并并校验出库记录
        Map<String, StockOperationRecord> mergedRecords = mergeOutboundRecords(outbound.getRecords());
        List<WorkteamStock> validStocks = validateAndUpdateStocks(mergedRecords, outbound.getIdWorkteam(), workTeamInfo);
        updateBatchById(validStocks);
        // 生成出库单
        if (ObjectUtil.isNull(outbound.getSourceBillType())) {
            outbound.setSourceBillType(BoundTypeEnum.RETURN.getType());
        }
        WorkteamOutboundBill outboundBill = generateAndSaveOutboundBill(outbound, validStocks);
        // 记录库存流水
        transactionLogService.generateTransactionLog(outbound, validStocks, TransLogTypeEnum.OUTBOUND);
        return outboundBill.getBillCode();
    }

    // 合并出库记录
    private Map<String, StockOperationRecord> mergeOutboundRecords(List<StockOperationRecord> records) {
        return records.stream().collect(Collectors.toMap(StockOperationRecord::getIdStock, record -> record, (existing, newRecord) -> {
            existing.setQuantity(existing.getQuantity().add(newRecord.getQuantity()));
            return existing;
        }));
    }

    // 校验并更新库存
    private List<WorkteamStock> validateAndUpdateStocks(Map<String, StockOperationRecord> records, String workteamId, WorkTeamBaseInfo workTeamInfo) {
        return records.values().stream().map(record -> {
            WorkteamStock stock = lambdaQuery().eq(WorkteamStock::getIdStock, record.getIdStock()).eq(WorkteamStock::getIdWorkteam, workteamId).one();

            if (ObjectUtil.isNull(stock)) {
                throw new ServiceException("班组库存信息不存在: " + record.getIdStock());
            }
            if (stock.getAvailableNum().subtract(record.getQuantity()).compareTo(BigDecimal.ZERO) < 0) {
                throw new ServiceException("班组物料库存不足: " + record.getIdStock());
            }

            initializeStockInfo(stock, workTeamInfo);
            stock.setAvailableNum(stock.getAvailableNum().subtract(record.getQuantity()));
            stock.setExtantNum(stock.getExtantNum().subtract(record.getQuantity()));
            stock.setChanges(record.getQuantity());
            return stock;
        }).collect(Collectors.toList());
    }

    // 生成并保存出库单
    private WorkteamOutboundBill generateAndSaveOutboundBill(StockOutboundDTO outbound, List<WorkteamStock> stocks) {
        BigDecimal totalQuantity = stocks.stream().map(WorkteamStock::getChanges).reduce(BigDecimal.ZERO, BigDecimal::add);
        WorkteamOutboundBill outboundBill = BillGenerator.generateOutboundBill(outbound, totalQuantity);
        String outBillCode = remoteResourceService.genBIllCode(StockConst.OUT_BOUND_ID_RES);
        outboundBill.setBillCode(outBillCode);
        outboundBill.setIdResource(StockConst.OUT_BOUND_ID_RES);
        workteamOutboundBillService.save(outboundBill);
        return outboundBill;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String stockInbound(StockInboundDTO inboundDTO) {
        checkAndFillResNam(inboundDTO);
        // 验证班组信息是否存在
        WorkTeamBaseInfo workTeamInfo = validateWorkTeam(inboundDTO.getIdWorkteam());
        // 合并入库记录
        Map<String, MaterialOperationRecord> mergedRecords = mergeInboundRecords(inboundDTO.getRecords());
        // 获取现有库存信息并合并更新
        List<WorkteamStock> updatedStocks = processStocks(mergedRecords, workTeamInfo, inboundDTO.getOperator());
        saveOrUpdateBatch(updatedStocks);
        // 生成入库单并保存
        WorkteamInboundBill inboundBill = createInboundBill(inboundDTO, mergedRecords);
        // 生成流水记录
        transactionLogService.generateTransactionLog(inboundDTO, updatedStocks, TransLogTypeEnum.INBOUND);
        return inboundBill.getBillCode();
    }

    private WorkTeamBaseInfo validateWorkTeam(String teamId) {
        WorkTeamBaseInfo workTeamInfo = remoteWorkTeamService.getWorkTeamById(teamId);
        if (ObjectUtil.isNull(workTeamInfo)) {
            throw new ServiceException("班组信息不存在");
        }
        String idWarehouse = workTeamInfo.getIdWarehouse();
        if (StrUtil.isBlank(idWarehouse)) {
            throw new ServiceException("班组未绑定仓库");
        }
        return workTeamInfo;
    }

    private Map<String, MaterialOperationRecord> mergeInboundRecords(List<MaterialOperationRecord> records) {
        return records.stream().collect(Collectors.toMap(MaterialOperationRecord::getUniqueKey, record -> record, (existing, incoming) -> {
            existing.setQuantity(existing.getQuantity().add(incoming.getQuantity()));
            return existing;
        }));
    }

    private List<WorkteamStock> processStocks(Map<String, MaterialOperationRecord> mergedRecords, WorkTeamBaseInfo workTeamInfo, String operator) {
        // 提前查询数据库中现有的库存数据
        List<String> materialIds = mergedRecords.values().stream().map(MaterialOperationRecord::getIdMaterial).distinct().collect(Collectors.toList());

        Map<String, WorkteamStock> existingStocks =
            lambdaQuery().in(CollectionUtil.isNotEmpty(materialIds), WorkteamStock::getIdMaterial, materialIds).eq(WorkteamStock::getIdWorkteam, workTeamInfo.getIdWorkteam())
                .list().stream().collect(Collectors.toMap(stock -> generateStockKey(stock.getIdMaterial(), stock.getBatchCode()), stock -> stock));

        BigDecimal totalQuantity = BigDecimal.ZERO;

        List<WorkteamStock> updatedStocks = new ArrayList<>();
        for (MaterialOperationRecord record : mergedRecords.values()) {
            String stockKey = generateStockKey(record.getIdMaterial(), record.getBatchCode());
            WorkteamStock stock = existingStocks.getOrDefault(stockKey, workTeamStockConvert.materialToStock(record));

            // 初始化库存信息
            initializeStockInfo(stock, workTeamInfo);

            // 更新库存数量
            stock.setChanges(record.getQuantity());
            stock.setAvailableNum(stock.getAvailableNum().add(record.getQuantity()));
            stock.setExtantNum(stock.getExtantNum().add(record.getQuantity()));
            stock.setCreator(operator);
            stock.setCreateTime(LocalDateTime.now());
            stock.setUpdater(operator);
            stock.setUpdateTime(LocalDateTime.now());
            stock.setReceiveTime(LocalDateTime.now());
            stock.setInventoryName(record.getInventoryName());

            totalQuantity = totalQuantity.add(record.getQuantity());
            updatedStocks.add(stock);
        }
        return updatedStocks;
    }

    private WorkteamInboundBill createInboundBill(StockInboundDTO inboundDTO, Map<String, MaterialOperationRecord> mergedRecords) {
        BigDecimal totalQuantity = mergedRecords.values().stream().map(MaterialOperationRecord::getQuantity).reduce(BigDecimal.ZERO, BigDecimal::add);
        if (ObjectUtil.isNull(inboundDTO.getSourceBillType())) {
            inboundDTO.setSourceBillType(BoundTypeEnum.RECEIVE.getType());
        }
        WorkteamInboundBill inboundBill = BillGenerator.generateInboundBill(inboundDTO, totalQuantity);
        String inBillCode = remoteResourceService.genBIllCode(StockConst.IN_BOUND_ID_RES);
        inboundBill.setBillCode(inBillCode);
        inboundBill.setIdResource(StockConst.IN_BOUND_ID_RES);
        workteamInboundBillService.save(inboundBill);
        return inboundBill;
    }

    private void initializeStockInfo(WorkteamStock stock, WorkTeamBaseInfo workTeamInfo) {
        stock.setIdWarehouse(workTeamInfo.getIdWarehouse());
        stock.setPkWarehouse(workTeamInfo.getPkWarehouse());
        stock.setWarehouseName(workTeamInfo.getWarehouseName());
        stock.setIdWhpos(workTeamInfo.getIdWhpos());
        stock.setPkWhpos(workTeamInfo.getPkWhpos());
        stock.setWhposName(workTeamInfo.getWhposName());
        stock.setIdOrg(workTeamInfo.getIdOrg());
        stock.setIdBizunit(workTeamInfo.getIdBizunit());
        stock.setIdWorkteam(workTeamInfo.getIdWorkteam());
        stock.setWorkteamName(workTeamInfo.getName());
        stock.setOrgName(workTeamInfo.getOrgName());
        stock.setBizunitName(workTeamInfo.getBizunitName());
    }

    private String generateStockKey(String materialId, String batchCode) {
        return materialId + ":" + (batchCode == null ? "DEFAULT" : batchCode);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void stockLock(StockLockReleaseDTO records) {
        processStockOperation(records, true);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void stockRelease(StockLockReleaseDTO records) {
        processStockOperation(records, false);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void stockApply(StockLockReleaseDTO apply) {
        //参数拆分成锁定和释放两部分列表
        Map<Boolean, List<StockOperationRecord>> splitMap =
            apply.getRecords().stream().collect(Collectors.partitioningBy(record -> record.getQuantity().compareTo(BigDecimal.ZERO) > 0));
        List<StockOperationRecord> stockRelease = splitMap.get(false);
        List<StockOperationRecord> stockLock = splitMap.get(true);
        //库存释放
        if (ObjectUtil.isNotEmpty(stockRelease)) {
            stockRelease.forEach(record -> record.setQuantity(record.getQuantity().negate()));
            apply.setRecords(stockRelease);
            stockRelease(apply);
        }
        //库存锁定
        if (ObjectUtil.isNotEmpty(stockLock)) {
            apply.setRecords(stockLock);
            stockLock(apply);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Dict stockExchange(StockExchangeDTO stockExchangeDTO) {
        StockOutboundDTO stockOutbound = new StockOutboundDTO();
        BeanUtil.copyProperties(stockExchangeDTO, stockOutbound);
        stockOutbound.setRecords(stockExchangeDTO.getReturns());
        stockOutbound.setSourceBillType(BoundTypeEnum.TRANSFER.getType());
        String outBillCode = stockOutbound(stockOutbound);
        StockInboundDTO stockInbound = new StockInboundDTO();
        stockInbound.setRecords(stockExchangeDTO.getReceives());
        stockInbound.setSourceBillType(BoundTypeEnum.TRANSFER.getType());
        BeanUtil.copyProperties(stockExchangeDTO, stockInbound);
        String inBillCode = stockInbound(stockInbound);
        return Dict.create().set("stockOutBillCode", outBillCode).set("stockInBillCode", inBillCode);
    }

    /**
     * 处理库存操作（锁定或释放）。
     *
     * @param lock   库存锁定/释放数据传输对象
     * @param isLock 标志：true 表示锁定，false 表示释放
     */
    @Transactional(rollbackFor = Exception.class)
    public void processStockOperation(StockLockReleaseDTO lock, boolean isLock) {
        // 合并记录
        Map<String, StockOperationRecord> merged = lock.getRecords().stream().collect(Collectors.toMap(StockOperationRecord::getIdStock, record -> record, (existing, incoming) -> {
            existing.setQuantity(existing.getQuantity().add(incoming.getQuantity()));
            return existing;
        }));
        checkAndFillResNam(lock);
        Boolean ignoreStockCheck = lock.getIgnoreStockCheck();
        // 库存校验和更新
        List<WorkteamStock> updatedStocks = merged.values().stream().map(record -> {
            WorkteamStock stock = lambdaQuery().eq(WorkteamStock::getIdStock, record.getIdStock()).one();
            if (ObjectUtil.isNull(stock)) {
                throw new ServiceException("班组库存信息不存在");
            }

            BigDecimal quantity = record.getQuantity();
            if (ignoreStockCheck == null || !ignoreStockCheck) {
                if (isLock) {
                    if (stock.getAvailableNum().subtract(quantity).compareTo(BigDecimal.ZERO) < 0) {
                        throw new ServiceException("班组库存可操作物料不足");
                    }
                    stock.setLocksNum(stock.getLocksNum().add(quantity));
                    stock.setAvailableNum(stock.getAvailableNum().subtract(quantity));
                } else {
                    if (stock.getLocksNum().subtract(quantity).compareTo(BigDecimal.ZERO) < 0) {
                        throw new ServiceException("班组库存可操作物料不足");
                    }
                    stock.setAvailableNum(stock.getAvailableNum().add(quantity));
                    stock.setLocksNum(stock.getLocksNum().subtract(quantity));
                }
            }
            stock.setChanges(quantity);
            stock.setUseTime(LocalDateTime.now());
            return stock;
        }).collect(Collectors.toList());

        // 批量更新库存
        updateBatchById(updatedStocks);
        for (WorkteamStock updatedStock : updatedStocks) {
            String idWorkteam = updatedStock.getIdWorkteam();
            WorkTeamBaseInfo workTeamById = remoteWorkTeamService.getWorkTeamById(idWorkteam);
            initializeStockInfo(updatedStock, workTeamById);
        }
        workteamStockLockRecordService.createWorkteamStockLockRecord(lock, updatedStocks, isLock);
    }

    private void checkAndFillResNam(BaseSourceBill sourceBill) {
        fillResNameIfPresent(sourceBill.getIdSourceRes(), sourceBill::setSourceResName);
        fillResNameIfPresent(sourceBill.getIdFirstRes(), sourceBill::setFirstResName);
    }

    private void fillResNameIfPresent(String resId, Consumer<String> nameSetter) {
        if (StrUtil.isNotBlank(resId)) {
            ResourceInfo resource = remoteResourceService.getResourceById(resId);
            if (resource != null) {
                nameSetter.accept(resource.getName());
            }
        }
    }

}

