package com.fls.stock.controller.workteam;

import cn.hutool.core.lang.Dict;
import com.fls.common.core.domain.ResponseData;
import com.fls.common.mybatis.core.page.PageResult;
import com.fls.stock.pojo.dto.GroupTransferDTO;
import com.fls.stock.pojo.dto.StockExchangeDTO;
import com.fls.stock.pojo.dto.StockInboundDTO;
import com.fls.stock.pojo.dto.StockLockReleaseDTO;
import com.fls.stock.pojo.dto.StockOutboundDTO;
import com.fls.stock.pojo.query.BaseAuthQuery;
import com.fls.stock.pojo.query.TeamStockDetailQuery;
import com.fls.stock.pojo.query.TeamStockLockQuery;
import com.fls.stock.pojo.query.TransLogDetailQuery;
import com.fls.stock.pojo.vo.TeamStockDetailVO;
import com.fls.stock.pojo.vo.TeamStockLockRecordVo;
import com.fls.stock.pojo.vo.TransactionLodDetailVO;
import com.fls.stock.service.woekteam.IWorkteamStockLockRecordService;
import com.fls.stock.service.woekteam.IWorkteamStockService;
import com.fls.stock.service.woekteam.IWorkteamTransactionDetailService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;

/**
 * 班组库存管理
 *
 * <AUTHOR>
 * @since 2024-12-10
 */
@Tag(name = "班组库存管理")
@Slf4j
@RestController
@RequestMapping("/team/stock")
@RequiredArgsConstructor
public class TeamStockManageController {
    private final IWorkteamStockService workteamStockService;

    private final IWorkteamTransactionDetailService workteamTransactionDetailService;

    private final IWorkteamStockLockRecordService workteamStockLockRecordService;

    @Operation(summary = "班组库存-明细查询")
    @PostMapping("/detail")
    public ResponseData<PageResult<TeamStockDetailVO>> getStockDetailsByPage(@Validated @RequestBody TeamStockDetailQuery teamStockDetailQuery) {
        return ResponseData.ok(workteamStockService.getWorkteamStockPage(teamStockDetailQuery));
    }

    @Operation(summary = "班组库存-组间调用")
    @PostMapping("/transfer")
    public ResponseData<Void> groupTransfer(@Validated @RequestBody GroupTransferDTO groupTransfer) {
        workteamStockService.groupTransfer(groupTransfer);
        return ResponseData.ok();
    }

    @Operation(summary = "班组库存-库存入库")
    @PostMapping("/in")
    public ResponseData<Dict> stockInbound(@Validated @RequestBody StockInboundDTO stockInbound) {
        String billCode = workteamStockService.stockInbound(stockInbound);
        return ResponseData.ok(Dict.create().set("stockInBillCode", billCode));
    }

    @Operation(summary = "班组库存-库存出库")
    @PostMapping("/out")
    public ResponseData<Dict> stockOutbound(@Validated @RequestBody StockOutboundDTO stockOutbound) {
        String outBillCode = workteamStockService.stockOutbound(stockOutbound);
        return ResponseData.ok(Dict.create().set("stockOutBillCode", outBillCode));
    }

    @Operation(summary = "班组库存-库存锁定")
    @PostMapping("/lock")
    public ResponseData<Void> stockLock(@Validated @RequestBody StockLockReleaseDTO lock) {
        workteamStockService.stockLock(lock);
        return ResponseData.ok();
    }

    @Operation(summary = "班组库存-库存释放")
    @PostMapping("/release")
    public ResponseData<Void> stockRelease(@Validated @RequestBody StockLockReleaseDTO release) {
        workteamStockService.stockRelease(release);
        return ResponseData.ok();
    }

    @Operation(summary = "班组库存-库存用料登记")
    @PostMapping("/apply")
    public ResponseData<Void> stockApply(@Validated @RequestBody StockLockReleaseDTO apply) {
        workteamStockService.stockApply(apply);
        return ResponseData.ok();
    }

    @Operation(summary = "班组库存-库存领料归还")
    @PostMapping("/return")
    public ResponseData<Dict> stockReturn(@Validated @RequestBody StockOutboundDTO stockReturn) {
        return stockOutbound(stockReturn);
    }

    @Operation(summary = "班组库存-库存领料退换")
    @PostMapping("/exchange")
    public ResponseData<Dict> stockExchange(@Validated @RequestBody StockExchangeDTO stockExchangeDTO) {
        Dict dict = workteamStockService.stockExchange(stockExchangeDTO);
        return ResponseData.ok(dict);
    }

    @Operation(summary = "班组库存-库存流水记录查询")
    @PostMapping("/transaction/log")
    public ResponseData<PageResult<TransactionLodDetailVO>> getTansLogDetailByPage(@Validated @RequestBody TransLogDetailQuery transLogDetailQuery) {
        PageResult<TransactionLodDetailVO> transLogPage = workteamTransactionDetailService.getTransLogPage(transLogDetailQuery);
        return ResponseData.ok(transLogPage);
    }

    @Operation(summary = "班组库存-库存流水记录导出")
    @PostMapping("/transaction/export")
    public void transactionExport(@RequestBody BaseAuthQuery authQuery, HttpServletResponse response) {
        workteamTransactionDetailService.exportTransLog(authQuery, response);
    }

    @Operation(summary = "班组库存-锁定记录查询")
    @PostMapping("/lock/log")
    public ResponseData<PageResult<TeamStockLockRecordVo>> getLockLogByPage(@Validated @RequestBody TeamStockLockQuery lockQuery) {
        return ResponseData.ok(workteamStockLockRecordService.getLockRecordPage(lockQuery));
    }


}
