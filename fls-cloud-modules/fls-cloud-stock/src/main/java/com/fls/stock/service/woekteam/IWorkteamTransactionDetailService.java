package com.fls.stock.service.woekteam;

import com.fls.common.mybatis.core.page.PageResult;
import com.fls.stock.entity.WorkteamTransactionDetail;
import com.fls.stock.pojo.query.BaseAuthQuery;
import com.fls.stock.pojo.query.TransLogDetailQuery;
import com.fls.stock.pojo.vo.TransactionLodDetailVO;
import com.github.yulichang.base.MPJBaseService;

import javax.servlet.http.HttpServletResponse;

/**
 * 班组库存流水明细表(WorkteamTransactionDetail)表服务接口
 *
 * <AUTHOR>
 * @since 2024-12-11 09:08:06
 */
public interface IWorkteamTransactionDetailService extends MPJBaseService<WorkteamTransactionDetail> {

    /**
     * 填充流水明细扩展属性
     *
     * @param detail 流水明细
     */
    void fillExProperties(WorkteamTransactionDetail detail);

    /**
     * 班组流水分页查询
     *
     * @param transLogDetailQuery 查询条件
     * @return 分页查询结果
     */
    PageResult<TransactionLodDetailVO> getTransLogPage(TransLogDetailQuery transLogDetailQuery);

    /**
     * 导出出入库流水记录
     *
     * @param authQuery 权限查询
     * @param response  请求响应
     */
    void exportTransLog(BaseAuthQuery authQuery, HttpServletResponse response);
}

