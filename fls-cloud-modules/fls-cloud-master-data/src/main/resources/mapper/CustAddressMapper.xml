<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.fls.master.mapper.CustAddressMapper">
    <select id="selectAllocPAge" resultType="com.fls.master.pojo.vo.CustAllocationVO">
        select tcal.id_custallocation as idAllocation,
        tcal.name as allocationName,
        tba.id_address as idAllocationAddress,
        tba.detail as allocationAddress,
        tba.code as allocationAddressCode,
        tcb.id_customer as idCust,
        tcb.name as custName,
        tcb.code as custCode,
        tcb.address as custAddress
        from t_cust_allocation tcal
        left join t_cust_baseinfo tcb on tcal.id_customer = tcb.id_customer
        left join t_base_address tba on tcal.id_address = tba.id_address
        <where>
            tcal.delete_flag = '0' and tcal.status = '2'
            <if test="query.keyword!=null and query.keyword!=''">
                and (locate(#{query.keyword},tcb.name) or locate(#{query.keyword},tcb.code) or
                locate(#{query.keyword},tba.detail) or locate(#{query.keyword},tba.code) or
                locate(#{query.keyword},tcb.address))
            </if>
            <if test="query.name!=null and query.name!=''">
                and (locate(#{query.name},tcal.name))
            </if>
            <choose>
                <when test="privateIdOrg != null and privateIdOrg.size() > 0">
                    and tcb.id_customer in (
                    select tco.id_customer from t_cust_org tco
                    where tco.id_org IN
                    <foreach item="item" collection="privateIdOrg" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                    )
                </when>
                <otherwise>
                    and 1 = 0
                </otherwise>
            </choose>
        </where>
    </select>
</mapper>
