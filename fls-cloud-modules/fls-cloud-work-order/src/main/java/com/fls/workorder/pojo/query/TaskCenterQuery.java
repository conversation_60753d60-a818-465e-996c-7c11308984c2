package com.fls.workorder.pojo.query;

import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;

/**
 * 工单任务列表请求
 *
 * <AUTHOR>
 * @since 2024-12-27
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class TaskCenterQuery extends BaseAuthQuery{
    /**
     * 任务状态
     */
    private String status;

    /**
     * 任务名称
     */
    private String name;

    /**
     * 任务编码
     */
    private String code;

    /**
     * 来源编号
     */
    private String sourceCode;

    /**
     * 资产编号
     */
    private String assetCode;

    /**
     * 经营主体id
     */
    private String idBizunit;

    /**
     * 来源资源id
     */
    private String idResource;

    /**
     * 开始时间
     */
    private String beginDate;

    /**
     * 结束时间
     */
    private String endDate;

    /**
     * 办理人
     */
    private String assignee;

    /**
     * 候选人
     */
    private String candidate;

    /**
     * 当前页码
     */
    @Min(value = 1, message = "页码最小不能小于1")
    private Integer pageNo = 1;

    /**
     * 每页条数
     */
    @Min(value = 1, message = "页记录大小最小不能小于1")
    @Max(value = 5000, message = "页记录大小最大不能超过5000")
    private Integer pageSize = 20;
}
